<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC "-//Puppy Crawl//DTD Check Configuration 1.3//EN" "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">
<module name="Checker">
    <!-- 编码设置 -->
    <property name="charset" value="UTF-8"/>
    
    <!-- 文件缩进设置 -->
    <property name="tabWidth" value="4"/>
    
    <!-- 忽略自动生成的文件 -->
    <module name="SuppressionFilter">
        <property name="file" value="suppressions.xml"/>
    </module>
    
    <!-- 文件和目录检查 -->
    <module name="TreeWalker">
        <!-- 命名规范检查 -->
        <module name="TypeName">
            <property name="format" value="^[A-Z][a-zA-Z0-9]*$"/>
            <property name="message" value="类名必须使用大驼峰命名法，例如：UserInfo"/>
        </module>
        
        <module name="MethodName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <property name="message" value="方法名必须使用小驼峰命名法，例如：getUserInfo"/>
        </module>
        
        <module name="VariableName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <property name="message" value="变量名必须使用小驼峰命名法，例如：userInfo"/>
        </module>
        
        <module name="ConstantName">
            <property name="format" value="^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$"/>
            <property name="message" value="常量名必须全部大写，单词间用下划线分隔，例如：MAX_USER_COUNT"/>
        </module>
        
        <!-- 代码格式检查 -->
        <module name="LineLength">
            <property name="max" value="120"/>
            <property name="ignorePattern" value="^import |^package |a href|href=|url=|a name|name=|src=|link=|@link |http://|https://"/>
            <property name="message" value="单行代码长度不超过120个字符"/>
        </module>
        
        <module name="EmptyBlock">
            <property name="option" value="TEXT"/>
            <property name="tokens" value="LITERAL_TRY, LITERAL_CATCH, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH, LITERAL_WHILE, LITERAL_DO, LITERAL_FOR"/>
            <property name="message" value="空代码块应该有明确的注释说明原因"/>
        </module>
        
        <module name="NeedBraces">
            <property name="tokens" value="LITERAL_IF, LITERAL_ELSE, LITERAL_FOR, LITERAL_WHILE, LITERAL_DO"/>
            <property name="message" value="即使只有一行代码，也应该使用大括号"/>
        </module>
        
        <module name="LeftCurly">
            <property name="tokens" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF, LITERAL_FOR, LITERAL_WHILE, LITERAL_DO, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH, LITERAL_TRY, LITERAL_CATCH, LITERAL_FINALLY"/>
            <property name="option" value="nl"/>
            <property name="lineBreaksBefore" value="1"/>
            <property name="message" value="左大括号必须另起一行"/>
        </module>
        
        <module name="RightCurly">
            <property name="tokens" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF, LITERAL_FOR, LITERAL_WHILE, LITERAL_DO, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH, LITERAL_TRY, LITERAL_CATCH, LITERAL_FINALLY"/>
            <property name="option" value="alone"/>
            <property name="message" value="右大括号必须单独一行"/>
        </module>
        
        <module name="OneStatementPerLine">
            <property name="tokens" value="SEMI"/>
            <property name="message" value="每行只允许有一条语句"/>
        </module>
        
        <module name="NoWhitespaceAfter">
            <property name="tokens" value="LPAREN, RPAREN, LBRACK, LBRACE, DOT"/>
            <property name="message" value="左括号、左方括号、左大括号和点号后面不允许有空格"/>
        </module>
        
        <module name="NoWhitespaceBefore">
            <property name="tokens" value="RPAREN, RBRACK, RBRACE, SEMI, COMMA"/>
            <property name="message" value="右括号、右方括号、右大括号、分号和逗号前面不允许有空格"/>
        </module>
        
        <module name="WhitespaceAround">
            <property name="tokens" value="ASSIGN, BAND, BAND_ASSIGN, BOR, BOR_ASSIGN, BSR, BSR_ASSIGN, BXOR, BXOR_ASSIGN, COLON, DIV, DIV_ASSIGN, EQUAL, GE, GT, LAND, LE, LITERAL_ASSERT, LITERAL_CATCH, LITERAL_DO, LITERAL_ELSE, LITERAL_FINALLY, LITERAL_FOR, LITERAL_IF, LITERAL_RETURN, LITERAL_SYNCHRONIZED, LITERAL_TRY, LITERAL_WHILE, LOR, LT, MINUS, MINUS_ASSIGN, MOD, MOD_ASSIGN, NOT_EQUAL, PLUS, PLUS_ASSIGN, QUESTION, SL, SL_ASSIGN, SR, SR_ASSIGN, STAR, STAR_ASSIGN, TYPE_EXTENSION_AND"/>
            <property name="message" value="操作符前后必须有空格"/>
        </module>
        
        <!-- 代码质量检查 -->
        <module name="MagicNumber">
            <property name="ignoreNumbers" value="0, 1, 2, 3, 4, 5, 100, 1000, 10000"/>
            <property name="message" value="不允许出现魔法数字，应该使用有意义的常量"/>
        </module>
        
        <module name="MissingSwitchDefault">
            <property name="message" value="switch语句必须包含default分支"/>
        </module>
        
        <module name="AvoidNestedBlocks">
            <property name="max" value="3"/>
            <property name="message" value="嵌套代码块不超过3层"/>
        </module>
        
        <module name="AvoidInlineConditionals">
            <property name="message" value="避免使用内联条件表达式，提高代码可读性"/>
        </module>
        
        <module name="ArrayTypeStyle">
            <property name="style" value="C"/>
            <property name="message" value="数组类型定义应使用C风格，如int[] array而非int array[]"/>
        </module>
        
        <module name="TodoComment">
            <property name="format" value="TODO \([^)]+\): .+"/>
            <property name="message" value="TODO注释必须包含负责人，格式为TODO (负责人): 描述"/>
        </module>
        
        <module name="CommentContent">
            <property name="format" value="[^a-zA-Z0-9\u4e00-\u9fa5\s\,\.\!\?\:\;\'\"\(\)\[\]\{\}\-\_\+\=\*\/\&\%\$\#\@\`\~]"/>
            <property name="message" value="注释中不允许使用非中英文、数字、标点符号的字符"/>
        </module>
        
        <!-- 安全检查 -->
        <module name="IllegalInstantiation">
            <property name="className" value="java.util.Date"/>
            <property name="message" value="避免使用java.util.Date，应使用java.time包下的类"/>
        </module>
        
        <module name="AvoidEscapedUnicodeCharacters">
            <property name="allowEscapesForControlCharacters" value="true"/>
            <property name="allowByTailComment" value="true"/>
            <property name="allowNonPrintableEscapes" value="true"/>
            <property name="message" value="避免使用Unicode转义字符，直接使用对应的字符"/>
        </module>
    </module>
</module>
