<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
          "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
          "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<!--
    Checkstyle configuration that checks the Google coding conventions from Google Java Style
    that can be found at https://google.github.io/styleguide/javaguide.html.
    Checkstyle is very configurable. Be sure to read the documentation at
    http://checkstyle.sf.net (or in your downloaded distribution).
    To completely disable a check, just comment it out or delete it from the file.
    Authors: <AUTHORS>
 -->

<module name = "Checker">
    <property name="charset" value="UTF-8"/>
    <property name="severity" value="warning"/>
    <property name="fileExtensions" value="java, properties, xml"/>

    <!-- 行中不允许出现tab制表符 -->
    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
    </module>

    <module name="LineLength">
        <property name="max" value="170"/>
        <property name="ignorePattern" value="^package.*|^import.*|a href|href|http://|https://|ftp://"/>
        <message key="maxLineLen" value="行字符数超过170个"/>
    </module>


    <module name="TreeWalker">
        <module name="OuterTypeFilename"/>
        <module name="IllegalTokenText">
            <property name="tokens" value="STRING_LITERAL, CHAR_LITERAL"/>
            <property name="format" value="\\u00(08|09|0(a|A)|0(c|C)|0(d|D)|22|27|5(C|c))|\\(0(10|11|12|14|15|42|47)|134)"/>
            <property name="message" value="Avoid using corresponding octal or Unicode escape."/>
        </module>
     
        <module name="OneTopLevelClass"/>
        <module name="NoLineWrap"/>
        <module name="EmptyBlock">
            <property name="option" value="TEXT"/>
            <property name="tokens" value="LITERAL_TRY, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH"/>
        </module>
        <module name="EmptyCatchBlock">
            <property name="exceptionVariableName" value="ignored"/>
        </module>
        <module name="NeedBraces">
            <message key="needBraces" value="''{0}'' 结构没有用大括号 '''{}'''s"/>
        </module>
        <module name="LeftCurly">
            <message key="line.previous" value="左侧大括号没有放在前一行代码的行尾"/>
        </module>
        <module name="RightCurly"/>
        <module name="RightCurly">
            <property name="option" value="alone"/>
            <property name="tokens" value="CLASS_DEF, METHOD_DEF, CTOR_DEF, LITERAL_FOR, LITERAL_WHILE, LITERAL_DO, STATIC_INIT, INSTANCE_INIT"/>
        </module>
        <module name="AvoidNestedBlocks"/>
        <module name="WhitespaceAround">
            <property name="allowEmptyConstructors" value="true"/>
            <property name="allowEmptyMethods" value="true"/>
            <property name="allowEmptyTypes" value="true"/>
            <property name="allowEmptyLoops" value="true"/>
            <message key="ws.notFollowed" value="WhitespaceAround: ''{0}'' is not followed by whitespace. Empty blocks may only be represented as '{}' when not part of a multi-block statement (4.1.3)"/>
            <message key="ws.notPreceded" value="WhitespaceAround: ''{0}'' is not preceded with whitespace."/>
        </module>
        <module name="NoFinalizer"/>
        <module name="GenericWhitespace">
            <message key="ws.followed" value="GenericWhitespace ''{0}'' is followed by whitespace."/>
             <message key="ws.preceded" value="GenericWhitespace ''{0}'' is preceded with whitespace."/>
             <message key="ws.illegalFollow" value="GenericWhitespace ''{0}'' should followed by whitespace."/>
             <message key="ws.notPreceded" value="GenericWhitespace ''{0}'' is not preceded with whitespace."/>
        </module>
        <module name="OneStatementPerLine"/>
        <module name="MultipleVariableDeclarations">
            <message key="multiple.variable.declarations.comma" value="每一行只能有一次变量声明"/>
            <message key="multiple.variable.declarations" value="每一行只能定义一个变量"/>
        </module>
        <module name="ArrayTypeStyle">
            <message key="array.type.style" value="数组定义没有采取int[] index这种方式"/>
        </module>
        <module name="MissingSwitchDefault">
            <message key="missing.switch.default" value="switch 语句后边没有 default 语句"/>
        </module>
        <module name="FallThrough"/>
        <module name="UpperEll"/>
        <module name="ModifierOrder"/>
        <module name="EmptyLineSeparator">
            <property name="allowNoEmptyLineBetweenFields" value="true"/>
        </module>
        <module name="SeparatorWrap">
            <property name="tokens" value="DOT"/>
            <property name="option" value="nl"/>
        </module>
        <module name="SeparatorWrap">
            <property name="tokens" value="COMMA"/>
            <property name="option" value="EOL"/>
        </module>

        <!-- 命名 -->
        <module name="PackageName">
            <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
            <message key="name.invalidPattern" value="包名 ''{0}'' 要匹配 ''{1}''.(小写字母开头，数字+小写字母组合)"/>
        </module>
        <module name="TypeName">
            <property name="tokens" value="CLASS_DEF"/>
            <message key="name.invalidPattern" value="类名 ''{0}''没有通过大驼峰命名法命名"/>
        </module>
        <module name="TypeName">
            <property name="tokens" value="INTERFACE_DEF"/>
            <message key="name.invalidPattern" value="接口名 ''{0}''没有通过大驼峰命名法命名"/>
        </module>
        <module name="MethodName">
            <property name="format" value="(^[a-z][a-zA-Z0-9]*$)"/>
            <message key="name.invalidPattern" value="方法名 ''{0}''没有通过小驼峰命名法命名"/>
        </module>
        <module name="MemberName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="​成员变量名 ''{0}''没有通过小驼峰命名法命名"/>
        </module>
        <module name="LocalVariableName">
            <property name="tokens" value="VARIABLE_DEF"/>
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <property name="allowOneCharVarInForLoop" value="true"/>
            <message key="name.invalidPattern" value="​局部变量名 ''{0}''没有通过小驼峰命名法命名"/>
        </module>
        <module name="LocalFinalVariableName" >
            <message key="name.invalidPattern" value="局部Final变量名 ''{0}''没有通过小驼峰命名法命名"/>
        </module>
        <module name="StaticVariableName">
            <message key="name.invalidPattern" value="Static变量名 ''{0}''没有通过小驼峰命名法命名"/>
        </module>
        <module name="ParameterName">
            <message key="name.invalidPattern" value="方法参数 ''{0}'' 要匹配 ''{1}''"/>
        </module>
        <!--
        <module name="CatchParameterName">
            <property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="Catch parameter name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="ClassTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern" value="Class type name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="MethodTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern" value="Method type name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="InterfaceTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern" value="Interface type name ''{0}'' must match pattern ''{1}''."/>
        </module>
        -->

        <!-- 缩进 -->
        <module name="Indentation">
            <property name="basicOffset" value="4"/>
            <property name="braceAdjustment" value="0"/>
            <property name="caseIndent" value="4"/>
            <property name="throwsIndent" value="4"/>
            <property name="lineWrappingIndentation" value="8"/>
            <property name="arrayInitIndent" value="8"/>
        </module>
        <module name="AbbreviationAsWordInName">
            <property name="ignoreFinal" value="false"/>
            <property name="allowedAbbreviationLength" value="0"/>
        </module>
        <module name="OverloadMethodsDeclarationOrder"/>
        <module name="VariableDeclarationUsageDistance">
            <property name="allowedDistance" value="5"/>
            <property name="ignoreVariablePattern" value="^stub.*|^mock.*"/>
        </module>
        <module name="MethodParamPad"/>
        <module name="OperatorWrap">
            <property name="option" value="EOL"/>
            <property name="tokens" value="BAND, BOR, BSR, BXOR, DIV, EQUAL, GE, GT, LAND, LE, LITERAL_INSTANCEOF, LOR, LT, MINUS, MOD, NOT_EQUAL, PLUS, QUESTION, SL, SR, STAR "/>
        </module>
        <module name="AnnotationLocation">
            <property name="tokens" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF"/>
        </module>
        <module name="AnnotationLocation">
            <property name="tokens" value="VARIABLE_DEF"/>
            <property name="allowSamelineMultipleAnnotations" value="true"/>
        </module>

        <module name="NonEmptyAtclauseDescription"/>
        <module name="AtclauseOrder">
            <property name="tagOrder" value="@param, @return, @throws, @deprecated"/>
            <property name="target" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF, VARIABLE_DEF"/>
        </module>
        <module name="JavadocTagContinuationIndentation"/>
        <module name="MissingJavadocMethod">
            <property name="allowMissingPropertyJavadoc" value="true" />
        </module>

        <module name="SingleLineJavadoc">
            <property name="ignoreInlineTags" value="false"/>
        </module>
     

        <module name="BooleanExpressionComplexity">
            <property name="max" value="7"/>
        </module>
        <module name="InnerAssignment"/>
        <module name="CyclomaticComplexity">
            <property name="max" value="20"/>
        </module>
        <module name="NestedForDepth">
            <property name="max" value="6"/>
        </module>
        <module name="NestedIfDepth">
            <property name="max" value="6"/>
        </module>
        <module name="NestedTryDepth">
            <property name="max" value="6"/>
        </module>
        <module name="MethodLength">
            <property name="max" value="100"/>
        </module>
        <module name="ParameterNumber">
            <property name="max" value="10"/>
            <property name="ignoreOverriddenMethods" value="true"/>
            <property name="tokens" value="METHOD_DEF"/>
        </module>
        <!-- 用equals进行字符串比较时，常量字符串必须出现在equals的左侧 -->
        <module name="EqualsAvoidNull"/>
        <module name="SimplifyBooleanReturn"/>
        <module name="SimplifyBooleanExpression">
            <message key="simplify.expression" value="布尔表达式的复杂度不能超过7"/>
        </module>

        <!-- 2024-06-14 新增checkstyle -->
        <!-- 检查是否从非法的包中导入了类 -->
        <module name="IllegalImport"/>
        <!-- 检查是否导入了多余的包 -->
        <module name="RedundantImport"/>
         <!-- 没用的import检查，比如：1.没有被用到2.重复的3.import java.lang的4.import 与该类在同一个package的 -->
        <module name="UnusedImports"/>
  

        <!-- String的比较不能用!= 和 == -->
        <module name="StringLiteralEquality"/>
        <!-- 禁止使用System.out.println -->
        <module name="Regexp">
            <property name="format" value="System.out.println"/>
            <property name="illegalPattern" value="true"/>
            <property name="message" value="禁止使用System.out.println打印输出,请使用log打印输出"/>
        </module>
        <!-- 禁止使用printStackTrace打印堆栈信息 -->
        <module name="Regexp">
            <property name="format" value="printStackTrace()"/>
            <property name="illegalPattern" value="true"/>
            <property name="message" value="禁止使用printStackTrace打印堆栈信息,请使用log打印输出"/>
        </module>
        
        <!-- 常量名的检查（只允许大写），默认^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$ -->
        <module name="ConstantName">
            <message key="name.invalidPattern" value="常量名的定义只允许大写字母"/>
        </module>

        <!-- =============类设计检查============= -->
        <!-- 检查类是否被设计为可扩展的，如果是，则方法应该abstract、final或者是空的 -->
        <!-- <module name="DesignForExtension"/> -->
        <!-- 检查一个只有私有构造器的类是否被声明为final -->
        <module name="FinalClass"/>
        <!-- 确保工具类（在API中只有静态方法和字段的类）没有任何公有构造器 
        <module name="HideUtilityClassConstructor"/>
        -->
        <!--　检查接口是否只定义了变量而没有定义方法，因为接口应该用来描述一个类型，所以只定义变量而不定义方法是不恰当的 allowMarkerInterfaces: 是否检查空接口 -->
        <module name="InterfaceIsType">  
            <property name="allowMarkerInterfaces" value="true"/>  
        </module>
        <!--　将异常抛出语句的数量配置为一个指定的限值（默认值为1）-->
         <module name="ThrowsCount">  
            <property name="max" value="7"/>  
        </module>
        <!--　检查类成员的可见性。
            只有static final的类成员可以是公有的，其他的类成员必须是私有的，除非设置了protectedAllowed属性或packageAllowed属性
            packageAllowed: 变量包内成员可以访问
            protectedAllowed: 变量是受保护的
            publicMemberPattern: 可以公开访问的变量所匹配的命名形式 -->
        <module name="VisibilityModifier">  
            <property name="packageAllowed" value="false"/>  
            <property name="protectedAllowed" value="false"/>  
            <property name="publicMemberPattern" value="^seriaVersionUID$"/>  
        </module>

    </module>
    
</module>
