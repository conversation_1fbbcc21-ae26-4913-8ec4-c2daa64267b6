<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC
          "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
          "https://checkstyle.org/dtds/configuration_1_3.dtd">

<!--
    阿里巴巴Java开发手册Checkstyle配置
    适用于Checkstyle 8.37及以上版本
    主要包含代码格式、命名规范、注释规范、代码逻辑等方面的检查
-->

<module name="Checker">
    <!-- 文件编码 -->
    <property name="charset" value="UTF-8"/>
    
    <!-- 检查文件扩展名 -->
    <property name="fileExtensions" value="java"/>
    
    <!-- 整体检查级别 -->
    <property name="severity" value="error"/>
    
    <!-- 文件头部注释检查 -->
    <module name="Header">
        <property name="headerFile" value="${checkstyle.header.file}"/>
    </module>
    
    <!-- 文件命名检查 -->
    <module name="TreeWalker">
        <!-- 文件名与主类名匹配检查 -->
        <module name="OuterTypeFilename"/>
        
        <!-- 包名规范检查 -->
        <module name="PackageName">
            <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
            <message key="name.invalidPattern" value="包名必须全部小写，且以字母开头，后续部分可以包含字母和数字"/>
        </module>
        
        <!-- 类名规范检查 -->
        <module name="TypeName">
            <property name="format" value="^[A-Z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="类名必须使用大驼峰命名法"/>
        </module>
        
        <!-- 方法名规范检查 -->
        <module name="MethodName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="方法名必须使用小驼峰命名法"/>
        </module>
        
        <!-- 变量名规范检查 -->
        <module name="MemberName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="成员变量名必须使用小驼峰命名法"/>
        </module>
        
        <!-- 常量名规范检查 -->
        <module name="ConstantName">
            <property name="format" value="^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$"/>
            <message key="name.invalidPattern" value="常量名必须全部大写，单词间用下划线分隔"/>
        </module>
        
        <!-- 缩进检查 -->
        <module name="Indentation">
            <property name="basicOffset" value="4"/>
            <property name="braceAdjustment" value="0"/>
            <property name="caseIndent" value="4"/>
            <property name="throwsIndent" value="4"/>
            <property name="lineWrappingIndentation" value="8"/>
        </module>
        
        <!-- 行长度检查 -->
        <module name="LineLength">
            <property name="max" value="120"/>
            <property name="ignorePattern" value="^package.*|^import.*|.*[=:].*|.*[>|<].*|.*[+].*|.*[-].*|.*[*].*|.*[/].*|.*[()].*|.*[{}].*|.*[[]].*|.*[;].*|.*[,].*|.*[\"]*.*|.*[']*.*"/>
        </module>
        
        <!-- 空行分隔检查 -->
        <module name="EmptyLineSeparator">
            <property name="tokens" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF, STATIC_INIT, INSTANCE_INIT"/>
        </module>
        
        <!-- 大括号使用检查 -->
        <module name="NeedBraces"/>
        
        <!-- 左大括号位置检查 -->
        <module name="LeftCurly">
            <property name="option" value="eol"/>
        </module>
        
        <!-- 右大括号位置检查 -->
        <module name="RightCurly">
            <property name="option" value="alone"/>
            <property name="tokens" value="CLASS_DEF, METHOD_DEF, CTOR_DEF, LITERAL_FOR, LITERAL_WHILE, LITERAL_DO"/>
        </module>
        
        <!-- 空格使用检查 -->
        <module name="WhitespaceAround">
            <property name="allowEmptyConstructors" value="false"/>
            <property name="allowEmptyMethods" value="false"/>
            <property name="allowEmptyTypes" value="false"/>
            <property name="allowEmptyLoops" value="false"/>
        </module>
        
        <!-- 操作符周围空格检查 -->
        <module name="OperatorWrap">
            <property name="option" value="EOL"/>
            <property name="tokens" value="ASSIGN, BAND, BAND_ASSIGN, BOR, BOR_ASSIGN, BSR, BSR_ASSIGN, BXOR, BXOR_ASSIGN, COLON, DIV, DIV_ASSIGN, EQUAL, GE, GT, LAND, LE, LITERAL_INSTANCEOF, LOR, LT, MINUS, MINUS_ASSIGN, MOD, MOD_ASSIGN, NOT_EQUAL, PLUS, PLUS_ASSIGN, QUESTION, SL, SL_ASSIGN, SR, SR_ASSIGN, STAR, STAR_ASSIGN"/>
        </module>
        
        <!-- 方法参数之间逗号后空格检查 -->
        <module name="CommaPad"/>
        
        <!-- 逗号后空格检查 -->
        <module name="WhitespaceAfter">
            <property name="tokens" value="COMMA"/>
        </module>
        
        <!-- 避免通配符导入 -->
        <module name="AvoidStarImport"/>
        
        <!-- 导入顺序检查 -->
        <module name="CustomImportOrder">
            <property name="sortImportsInGroupAlphabetically" value="true"/>
            <property name="customImportOrderRules" value="STATIC###THIRD_PARTY_PACKAGE###java###javax###org"/>
        </module>
        
        <!-- 方法长度检查 -->
        <module name="MethodLength">
            <property name="max" value="80"/>
        </module>
        
        <!-- 圈复杂度检查 -->
        <module name="CyclomaticComplexity">
            <property name="max" value="10"/>
        </module>
        
        <!-- 参数数量检查 -->
        <module name="ParameterNumber">
            <property name="max" value="5"/>
        </module>
        
        <!-- 避免空的catch块 -->
        <module name="EmptyCatchBlock"/>
        
        <!-- 避免空的finally块 -->
        <module name="EmptyFinallyBlock"/>
        
        <!-- 避免空的if块 -->
        <module name="EmptyIfStmt"/>
        
        <!-- 避免空的while块 -->
        <module name="EmptyWhileStmt"/>
        
        <!-- 避免使用魔法数字 -->
        <module name="MagicNumber">
            <property name="ignoreNumbers" value="0,1,-1"/>
        </module>
        
        <!-- 避免在循环中创建对象 -->
        <module name="AvoidInstantiatingObjectsInLoops"/>
        
        <!-- 避免使用finalizer -->
        <module name="NoFinalizer"/>
        
        <!-- 确保equals和hashCode方法同时存在 -->
        <module name="EqualsHashCode"/>
        
        <!-- 确保重写方法使用@Override注解 -->
        <module name="MissingOverride"/>
        
        <!-- 检查StringBuffer/StringBuilder的使用 -->
        <module name="StringBufferUsage"/>
        
        <!-- 检查异常处理 -->
        <module name="IllegalThrows"/>
        
        <!-- 检查方法声明 -->
        <module name="MethodDeclarationRestriction"/>
        
        <!-- 检查类成员顺序 -->
        <module name="DeclarationOrder"/>
        
        <!-- 检查不必要的修饰符 -->
        <module name="RedundantModifier"/>
        
        <!-- 检查switch语句中的default -->
        <module name="MissingSwitchDefault"/>
        
        <!-- 检查switch语句中的fall-through -->
        <module name="FallThrough"/>
        
        <!-- 检查方法注释 -->
        <module name="JavadocMethod">
            <property name="scope" value="public"/>
            <property name="allowMissingParamTags" value="false"/>
            <property name="allowMissingReturnTag" value="false"/>
            <property name="allowMissingThrowsTags" value="false"/>
        </module>
        
        <!-- 检查类注释 -->
        <module name="JavadocType">
            <property name="scope" value="public"/>
        </module>
        
        <!-- 检查变量注释 -->
        <module name="JavadocVariable">
            <property name="scope" value="public"/>
        </module>
        
        <!-- 检查注释格式 -->
        <module name="JavadocStyle"/>
        
        <!-- 检查代码中的TODO/FIXME标记 -->
        <module name="TodoComment">
            <property name="format" value="TODO|FIXME"/>
        </module>
        
        <!-- 检查数组定义格式 -->
        <module name="ArrayTypeStyle"/>
        
        <!-- 检查嵌套深度 -->
        <module name="NestedForDepth">
            <property name="max" value="3"/>
        </module>
        
        <!-- 检查嵌套if深度 -->
        <module name="NestedIfDepth">
            <property name="max" value="3"/>
        </module>
        
        <!-- 检查嵌套try深度 -->
        <module name="NestedTryDepth">
            <property name="max" value="2"/>
        </module>
        
        <!-- 检查重复代码 -->
        <module name="AvoidDuplicateLiterals"/>
        
        <!-- 检查字符串连接 -->
        <module name="StringLiteralEquality"/>
        
        <!-- 检查使用equals进行字符串比较 -->
        <module name="EqualsAvoidNull"/>
        
        <!-- 检查简化布尔表达式 -->
        <module name="SimplifyBooleanExpression"/>
        
        <!-- 检查简化布尔返回 -->
        <module name="SimplifyBooleanReturn"/>
    </module>
</module>
