<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC
          "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
          "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<!--
    合并的Checkstyle配置文件
    基于Uplus和阿里巴巴Java开发手册的规范
    适用于Checkstyle 8.37及以上版本
    主要包含代码格式、命名规范、注释规范、代码逻辑等方面的检查
-->

<module name="Checker">
    <!-- 基础配置 -->
    <property name="charset" value="UTF-8"/>
    <property name="severity" value="warning"/>
    <property name="fileExtensions" value="java, properties, xml"/>
    <property name="tabWidth" value="4"/>

    <!-- 文件编码和格式检查 -->
    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
        <message key="containsTab" value="文件中不允许出现tab制表符"/>
    </module>

    <!-- 行长度检查 -->
    <module name="LineLength">
        <property name="max" value="120"/>
        <property name="ignorePattern" value="^package.*|^import.*|a href|href|http://|https://|ftp://"/>
        <message key="maxLineLen" value="行字符数超过120个"/>
    </module>

    <module name="TreeWalker">
        <!-- 文件命名检查 -->
        <module name="OuterTypeFilename">
            <message key="type.mismatch" value="文件名必须与主类名匹配"/>
        </module>

        <!-- 包名规范检查 -->
        <module name="PackageName">
            <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
            <message key="name.invalidPattern" value="包名必须全部小写，且以字母开头，后续部分可以包含字母和数字"/>
        </module>

        <!-- 类名规范检查 -->
        <module name="TypeName">
            <property name="format" value="^[A-Z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="类名必须使用大驼峰命名法"/>
        </module>

        <!-- 接口名规范检查 -->
        <module name="TypeName">
            <property name="tokens" value="INTERFACE_DEF"/>
            <message key="name.invalidPattern" value="接口名必须使用大驼峰命名法"/>
        </module>

        <!-- 方法名规范检查 -->
        <module name="MethodName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="方法名必须使用小驼峰命名法"/>
        </module>

        <!-- 成员变量名规范检查 -->
        <module name="MemberName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="成员变量名必须使用小驼峰命名法"/>
        </module>

        <!-- 常量名规范检查 -->
        <module name="ConstantName">
            <property name="format" value="^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$"/>
            <message key="name.invalidPattern" value="常量名必须全部大写，单词间用下划线分隔"/>
        </module>

        <!-- 局部变量名规范检查 -->
        <module name="LocalVariableName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <property name="allowOneCharVarInForLoop" value="true"/>
            <message key="name.invalidPattern" value="局部变量名必须使用小驼峰命名法"/>
        </module>

        <!-- 代码格式检查 -->
        <module name="Indentation">
            <property name="basicOffset" value="4"/>
            <property name="braceAdjustment" value="0"/>
            <property name="caseIndent" value="4"/>
            <property name="throwsIndent" value="4"/>
            <property name="lineWrappingIndentation" value="8"/>
            <property name="arrayInitIndent" value="8"/>
            <message key="indentation.child.error" value="缩进不正确"/>
        </module>

        <!-- 大括号使用检查 -->
        <module name="NeedBraces">
            <message key="needBraces" value="if/else/for/while/do语句必须使用大括号"/>
        </module>

        <!-- 左大括号位置检查 -->
        <module name="LeftCurly">
            <property name="option" value="eol"/>
            <message key="line.previous" value="左大括号必须放在行尾"/>
        </module>

        <!-- 右大括号位置检查 -->
        <module name="RightCurly">
            <property name="option" value="alone"/>
            <property name="tokens" value="CLASS_DEF, METHOD_DEF, CTOR_DEF, LITERAL_FOR, LITERAL_WHILE, LITERAL_DO, STATIC_INIT, INSTANCE_INIT"/>
            <message key="line.alone" value="右大括号必须单独一行"/>
        </module>

        <!-- 空格使用检查 -->
        <module name="WhitespaceAround">
            <property name="allowEmptyConstructors" value="true"/>
            <property name="allowEmptyMethods" value="true"/>
            <property name="allowEmptyTypes" value="true"/>
            <property name="allowEmptyLoops" value="true"/>
            <message key="ws.notFollowed" value="操作符后面必须有空格"/>
            <message key="ws.notPreceded" value="操作符前面必须有空格"/>
        </module>

        <!-- 操作符换行检查 -->
        <module name="OperatorWrap">
            <property name="option" value="EOL"/>
            <property name="tokens" value="BAND, BOR, BSR, BXOR, DIV, EQUAL, GE, GT, LAND, LE, LITERAL_INSTANCEOF, LOR, LT, MINUS, MOD, NOT_EQUAL, PLUS, QUESTION, SL, SR, STAR"/>
            <message key="line.new" value="操作符必须放在行尾"/>
        </module>

        <!-- 导入检查 -->
        <module name="AvoidStarImport">
            <message key="import.avoidStar" value="禁止使用通配符导入"/>
        </module>

        <module name="CustomImportOrder">
            <property name="sortImportsInGroupAlphabetically" value="true"/>
            <property name="customImportOrderRules" value="STATIC###THIRD_PARTY_PACKAGE###java###javax###org"/>
            <message key="custom.import.order" value="导入顺序不正确"/>
        </module>

        <!-- 代码质量检查 -->
        <module name="MethodLength">
            <property name="max" value="100"/>
            <message key="maxLen.method" value="方法长度不能超过100行"/>
        </module>

        <module name="CyclomaticComplexity">
            <property name="max" value="10"/>
            <message key="cyclomaticComplexity" value="方法的圈复杂度不能超过10"/>
        </module>

        <module name="ParameterNumber">
            <property name="max" value="5"/>
            <property name="ignoreOverriddenMethods" value="true"/>
            <message key="maxParam" value="方法参数不能超过5个"/>
        </module>

        <!-- 嵌套深度检查 -->
        <module name="NestedForDepth">
            <property name="max" value="3"/>
            <message key="nested.for.depth" value="for循环嵌套不能超过3层"/>
        </module>

        <module name="NestedIfDepth">
            <property name="max" value="3"/>
            <message key="nested.if.depth" value="if语句嵌套不能超过3层"/>
        </module>

        <module name="NestedTryDepth">
            <property name="max" value="2"/>
            <message key="nested.try.depth" value="try语句嵌套不能超过2层"/>
        </module>

        <!-- 注释检查 -->
        <module name="JavadocMethod">
            <property name="scope" value="public"/>
            <property name="allowMissingParamTags" value="true"/>
            <property name="allowMissingThrowsTags" value="true"/>
            <property name="allowMissingReturnTag" value="true"/>
            <message key="javadoc.missing" value="缺少必要的JavaDoc注释"/>
        </module>

        <module name="JavadocType">
            <property name="scope" value="public"/>
            <message key="javadoc.missing" value="类缺少必要的JavaDoc注释"/>
        </module>

        <!-- 其他检查 -->
        <module name="MissingSwitchDefault">
            <message key="missing.switch.default" value="switch语句必须包含default分支"/>
        </module>

        <module name="EqualsAvoidNull">
            <message key="equals.avoid.null" value="使用equals比较字符串时，常量必须放在equals前面"/>
        </module>

        <module name="SimplifyBooleanExpression">
            <message key="simplify.expression" value="布尔表达式可以简化"/>
        </module>

        <module name="SimplifyBooleanReturn">
            <message key="simplify.return" value="布尔返回值可以简化"/>
        </module>

        <module name="ArrayTypeStyle">
            <message key="array.type.style" value="数组定义必须使用int[] style"/>
        </module>

        <module name="TodoComment">
            <property name="format" value="TODO \([^)]+\): .+"/>
            <message key="todo.match" value="TODO注释必须包含负责人，格式为TODO (负责人): 描述"/>
        </module>
    </module>
</module> 