<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
          "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
          "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<!--
    Checkstyle configuration that checks the Google coding conventions from Google Java Style
    that can be found at https://google.github.io/styleguide/javaguide.html.
    Checkstyle is very configurable. Be sure to read the documentation at
    http://checkstyle.sf.net (or in your downloaded distribution).
    To completely disable a check, just comment it out or delete it from the file.
    Authors: <AUTHORS>
 -->

<module name = "Checker">
    <property name="charset" value="UTF-8"/>
    <property name="severity" value="warning"/>
    <property name="fileExtensions" value="java, properties, xml"/>

    <!-- 行中不允许出现tab制表符 -->
    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
    </module>
    <module name="LineLength">
        <property name="max" value="120"/>
        <property name="ignorePattern" value="^package.*|^import.*|a href|href|http://|https://|ftp://"/>
        <message key="maxLineLen" value="行字符数超过120个"/>
    </module>

    <module name="TreeWalker">
        <module name="OuterTypeFilename"/>
        <module name="IllegalTokenText">
            <property name="tokens" value="STRING_LITERAL, CHAR_LITERAL"/>
            <property name="format" value="\\u00(08|09|0(a|A)|0(c|C)|0(d|D)|22|27|5(C|c))|\\(0(10|11|12|14|15|42|47)|134)"/>
            <property name="message" value="Avoid using corresponding octal or Unicode escape."/>
        </module>
        <!--module name="AvoidEscapedUnicodeCharacters">
            <property name="allowEscapesForControlCharacters" value="true"/>
            <property name="allowByTailComment" value="true"/>
            <property name="allowNonPrintableEscapes" value="true"/>
        </module-->

        <!-- Import引用时不能出现*号 -->
        <module name="AvoidStarImport">
            <message key="import.avoidStar" value="引用包时不能使用*号"/>
        </module>
        <!--module name="CustomImportOrder">
            <property name="specialImportsRegExp" value="com.google"/>
            <property name="sortImportsInGroupAlphabetically" value="true"/>
            <property name="customImportOrderRules" value="STATIC###SPECIAL_IMPORTS###THIRD_PARTY_PACKAGE###STANDARD_JAVA_PACKAGE"/>
        </module-->
        <module name="OneTopLevelClass"/>
        <module name="NoLineWrap"/>
        <module name="EmptyBlock">
            <property name="option" value="TEXT"/>
            <property name="tokens" value="LITERAL_TRY, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH"/>
        </module>
        <module name="EmptyCatchBlock">
            <property name="exceptionVariableName" value="ignored"/>
        </module>
        <module name="NeedBraces">
            <message key="needBraces" value="''{0}'' 结构没有用大括号 '''{}'''s"/>
        </module>
        <module name="LeftCurly">
            <message key="line.previous" value="左侧大括号没有放在前一行代码的行尾"/>
        </module>
        <module name="RightCurly"/>
        <module name="RightCurly">
            <property name="option" value="alone"/>
            <property name="tokens" value="CLASS_DEF, METHOD_DEF, CTOR_DEF, LITERAL_FOR, LITERAL_WHILE, LITERAL_DO, STATIC_INIT, INSTANCE_INIT"/>
        </module>
        <module name="AvoidNestedBlocks"/>
        <module name="WhitespaceAround">
            <property name="allowEmptyConstructors" value="true"/>
            <property name="allowEmptyMethods" value="true"/>
            <property name="allowEmptyTypes" value="true"/>
            <property name="allowEmptyLoops" value="true"/>
            <message key="ws.notFollowed" value="WhitespaceAround: ''{0}'' is not followed by whitespace. Empty blocks may only be represented as '{}' when not part of a multi-block statement (4.1.3)"/>
            <message key="ws.notPreceded" value="WhitespaceAround: ''{0}'' is not preceded with whitespace."/>
        </module>
        <module name="NoFinalizer"/>
        <module name="GenericWhitespace">
            <message key="ws.followed" value="GenericWhitespace ''{0}'' is followed by whitespace."/>
             <message key="ws.preceded" value="GenericWhitespace ''{0}'' is preceded with whitespace."/>
             <message key="ws.illegalFollow" value="GenericWhitespace ''{0}'' should followed by whitespace."/>
             <message key="ws.notPreceded" value="GenericWhitespace ''{0}'' is not preceded with whitespace."/>
        </module>
        <module name="OneStatementPerLine"/>
        <module name="MultipleVariableDeclarations">
            <message key="multiple.variable.declarations.comma" value="每一行只能有一次变量声明"/>
            <message key="multiple.variable.declarations" value="每一行只能定义一个变量"/>
        </module>
        <module name="ArrayTypeStyle">
            <message key="array.type.style" value="数组定义没有采取int[] index这种方式"/>
        </module>
        <module name="MissingSwitchDefault">
            <message key="missing.switch.default" value="switch 语句后边没有 default 语句"/>
        </module>
        <module name="FallThrough"/>
        <module name="UpperEll"/>
        <module name="ModifierOrder"/>
        <module name="EmptyLineSeparator">
            <property name="allowNoEmptyLineBetweenFields" value="true"/>
        </module>
        <module name="SeparatorWrap">
            <property name="tokens" value="DOT"/>
            <property name="option" value="nl"/>
        </module>
        <module name="SeparatorWrap">
            <property name="tokens" value="COMMA"/>
            <property name="option" value="EOL"/>
        </module>

        <!-- 命名 -->
        <module name="PackageName">
            <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
            <message key="name.invalidPattern" value="包名 ''{0}'' 要匹配 ''{1}''.(小写字母开头，数字+小写字母组合)"/>
        </module>
        <module name="TypeName">
            <property name="tokens" value="CLASS_DEF"/>
            <message key="name.invalidPattern" value="类名 ''{0}''没有通过大驼峰命名法命名"/>
        </module>
        <module name="TypeName">
            <property name="tokens" value="INTERFACE_DEF"/>
            <message key="name.invalidPattern" value="接口名 ''{0}''没有通过大驼峰命名法命名"/>
        </module>
        <module name="MethodName">
            <property name="format" value="(^[a-z][a-zA-Z0-9]*$)"/>
            <message key="name.invalidPattern" value="方法名 ''{0}''没有通过小驼峰命名法命名"/>
        </module>
        <module name="MemberName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="​成员变量名 ''{0}''没有通过小驼峰命名法命名"/>
        </module>
        <module name="LocalVariableName">
            <property name="tokens" value="VARIABLE_DEF"/>
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <property name="allowOneCharVarInForLoop" value="true"/>
            <message key="name.invalidPattern" value="​局部变量名 ''{0}''没有通过小驼峰命名法命名"/>
        </module>
        <module name="LocalFinalVariableName" >
            <message key="name.invalidPattern" value="局部Final变量名 ''{0}''没有通过小驼峰命名法命名"/>
        </module>
        <module name="StaticVariableName">
            <message key="name.invalidPattern" value="Static变量名 ''{0}''没有通过小驼峰命名法命名"/>
        </module>
        <module name="ParameterName">
            <message key="name.invalidPattern" value="方法参数 ''{0}'' 要匹配 ''{1}''"/>
        </module>
        <!--
        <module name="CatchParameterName">
            <property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="Catch parameter name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="ClassTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern" value="Class type name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="MethodTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern" value="Method type name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="InterfaceTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern" value="Interface type name ''{0}'' must match pattern ''{1}''."/>
        </module>
        -->

        <!-- 缩进 -->
        <module name="Indentation">
            <property name="basicOffset" value="4"/>
            <property name="braceAdjustment" value="0"/>
            <property name="caseIndent" value="4"/>
            <property name="throwsIndent" value="4"/>
            <property name="lineWrappingIndentation" value="8"/>
            <property name="arrayInitIndent" value="8"/>
        </module>
        <module name="AbbreviationAsWordInName">
            <property name="ignoreFinal" value="false"/>
            <property name="allowedAbbreviationLength" value="3"/>
        </module>
        <module name="OverloadMethodsDeclarationOrder"/>
        <module name="VariableDeclarationUsageDistance">
            <property name="allowedDistance" value="5"/>
            <property name="ignoreVariablePattern" value="^stub.*|^mock.*"/>
        </module>
        <module name="MethodParamPad"/>
        <module name="OperatorWrap">
            <property name="option" value="EOL"/>
            <property name="tokens" value="BAND, BOR, BSR, BXOR, DIV, EQUAL, GE, GT, LAND, LE, LITERAL_INSTANCEOF, LOR, LT, MINUS, MOD, NOT_EQUAL, PLUS, QUESTION, SL, SR, STAR "/>
        </module>
        <module name="AnnotationLocation">
            <property name="tokens" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF"/>
        </module>
        <module name="AnnotationLocation">
            <property name="tokens" value="VARIABLE_DEF"/>
            <property name="allowSamelineMultipleAnnotations" value="true"/>
        </module>

        <module name="NonEmptyAtclauseDescription"/>
        <module name="AtclauseOrder">
            <property name="tagOrder" value="@param, @return, @throws, @deprecated"/>
            <property name="target" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF, VARIABLE_DEF"/>
        </module>
        <module name="JavadocTagContinuationIndentation"/>
        <!-- <module name="JavadocMethod">
            <property name="scope" value="nothing"/>
            <property name="allowMissingParamTags" value="true"/>
            <property name="allowMissingThrowsTags" value="true"/>
            <property name="allowMissingReturnTag" value="true"/>
            <property name="minLineCount" value="2"/>
            <property name="allowedAnnotations" value="Override, Test"/>
            <property name="allowThrowsTagsForSubclasses" value="true"/>
            <property name="allowMissingPropertyJavadoc" value="true"/>
        </module> -->
        <module name="SingleLineJavadoc">
            <property name="ignoreInlineTags" value="false"/>
        </module>
        <!--
        <module name="SummaryJavadoc">
            <property name="forbiddenSummaryFragments" value="^@return the *|^This method returns |^A [{]@code [a-zA-Z0-9]+[}]( is a )"/>
        </module>
        <module name="JavadocParagraph">
            <property name="allowNewlineParagraph" value="true"/>
        </module>
        -->

        <module name="BooleanExpressionComplexity">
            <property name="max" value="7"/>
        </module>
        <module name="InnerAssignment"/>
        <module name="CyclomaticComplexity">
            <property name="max" value="10"/>
        </module>
        <module name="NestedForDepth">
            <property name="max" value="3"/>
        </module>
        <module name="NestedIfDepth">
            <property name="max" value="3"/>
        </module>
        <module name="NestedTryDepth">
            <property name="max" value="3"/>
        </module>
        <module name="MethodLength">
            <property name="max" value="100"/>
        </module>
        <module name="ParameterNumber">
            <property name="max" value="5"/>
            <property name="ignoreOverriddenMethods" value="true"/>
            <property name="tokens" value="METHOD_DEF"/>
        </module>
        <!-- 用equals进行字符串比较时，常量字符串必须出现在equals的左侧 -->
        <module name="EqualsAvoidNull"/>
        <module name="SimplifyBooleanReturn"/>
        <module name="SimplifyBooleanExpression">
            <message key="simplify.expression" value="布尔表达式的复杂度不能超过7"/>
        </module>
    </module>
</module>
